import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Car,
  Clock,
  CheckCircle,
  AlertTriangle,
  Plus,
  User,
  Phone,
  Mail,
  FileText,
  DollarSign
} from 'lucide-react';
import { bookingsAPI } from '../utils/mysqlDatabase';
import { bookingsAPI as localBookingsAPI } from '../utils/localDatabase';
import LoadingSpinner from './LoadingSpinner';

interface Booking {
  id: number;
  customer_name: string;
  vehicle_info: string;
  service_type: string;
  booking_date: string;
  status: string;
  estimated_cost: number;
  notes?: string;
  phone?: string;
  email?: string;
}

interface CustomerBookingProps {
  currentUser: any;
}

const CustomerBooking: React.FC<CustomerBookingProps> = ({ currentUser }) => {
  const [myBookings, setMyBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewBooking, setShowNewBooking] = useState(false);
  const [newBooking, setNewBooking] = useState({
    vehicle_info: '',
    service_type: '',
    booking_date: '',
    notes: '',
    phone: '',
    email: currentUser?.email || ''
  });

  const serviceTypes = [
    'Ganti Oli',
    'Tune Up',
    'Service AC',
    'Ganti Ban',
    'Service Rem',
    'Cuci Mobil',
    'Service Berkala',
    'Perbaikan Mesin',
    'Service Transmisi',
    'Lainnya'
  ];

  useEffect(() => {
    fetchMyBookings();
  }, [currentUser]);

  const fetchMyBookings = async () => {
    setLoading(true);
    try {
      // Get bookings from localStorage first (customer-specific)
      const savedBookings = localStorage.getItem(`bookings_${currentUser.email}`);
      if (savedBookings) {
        setMyBookings(JSON.parse(savedBookings));
      } else {
        // Fallback to database if no localStorage data
        try {
          const allBookings = await bookingsAPI.getAll();
          const customerBookings = (allBookings as Booking[]).filter(
            booking => booking.customer_name.toLowerCase().includes(currentUser.full_name.toLowerCase()) ||
                      booking.email === currentUser.email
          );
          setMyBookings(customerBookings);
        } catch (mysqlError) {
          console.warn('MySQL failed, using localStorage:', mysqlError);
          const allBookings = await localBookingsAPI.getAll();
          const customerBookings = (allBookings as Booking[]).filter(
            booking => booking.customer_name.toLowerCase().includes(currentUser.full_name.toLowerCase()) ||
                      booking.email === currentUser.email
          );
          setMyBookings(customerBookings);
        }
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setMyBookings([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewBooking(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmitBooking = async (e: React.FormEvent) => {
    e.preventDefault();

    const bookingData = {
      id: Date.now(),
      customer_name: currentUser.full_name,
      vehicle_info: newBooking.vehicle_info,
      service_type: newBooking.service_type,
      booking_date: newBooking.booking_date,
      status: 'Menunggu',
      estimated_cost: getEstimatedCost(newBooking.service_type),
      notes: newBooking.notes,
      phone: newBooking.phone,
      email: newBooking.email
    };

    try {
      // Save to localStorage for customer tracking
      const existingBookings = localStorage.getItem(`bookings_${currentUser.email}`);
      const bookings = existingBookings ? JSON.parse(existingBookings) : [];
      bookings.unshift(bookingData); // Add new booking at the beginning
      localStorage.setItem(`bookings_${currentUser.email}`, JSON.stringify(bookings));

      // Try MySQL first, fallback to localStorage
      try {
        await bookingsAPI.create(bookingData);
      } catch (mysqlError) {
        console.warn('MySQL failed, using localStorage:', mysqlError);
        await localBookingsAPI.create(bookingData);
      }

      // Reset form and refresh bookings
      setNewBooking({
        vehicle_info: '',
        service_type: '',
        booking_date: '',
        notes: '',
        phone: '',
        email: currentUser?.email || ''
      });
      setShowNewBooking(false);
      fetchMyBookings();

      alert('Booking berhasil dibuat!');
    } catch (error) {
      console.error('Error creating booking:', error);
      alert('Gagal membuat booking. Silakan coba lagi.');
    }
  };

  const getEstimatedCost = (serviceType: string) => {
    const costs: { [key: string]: number } = {
      'Ganti Oli': 150000,
      'Tune Up': 300000,
      'Service AC': 200000,
      'Ganti Ban': 800000,
      'Service Rem': 250000,
      'Cuci Mobil': 50000,
      'Service Berkala': 400000,
      'Perbaikan Mesin': 500000,
      'Service Transmisi': 600000,
      'Lainnya': 100000
    };
    return costs[serviceType] || 100000;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Sedang Dikerjakan':
        return 'bg-blue-100 text-blue-800';
      case 'Menunggu':
        return 'bg-yellow-100 text-yellow-800';
      case 'Selesai':
        return 'bg-green-100 text-green-800';
      case 'Dijadwalkan':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Sedang Dikerjakan':
        return <Clock className="h-4 w-4" />;
      case 'Menunggu':
        return <AlertTriangle className="h-4 w-4" />;
      case 'Selesai':
        return <CheckCircle className="h-4 w-4" />;
      case 'Dijadwalkan':
        return <Calendar className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
              <Calendar className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">My Bookings</h1>
              <p className="text-gray-600">Kelola booking service Anda</p>
            </div>
          </div>
          <button
            onClick={() => setShowNewBooking(true)}
            className="bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-2 rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-300 flex items-center space-x-2"
          >
            <Plus className="h-5 w-5" />
            <span>New Booking</span>
          </button>
        </div>
      </div>

      {/* New Booking Form */}
      {showNewBooking && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Create New Booking</h2>
          <form onSubmit={handleSubmitBooking} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Vehicle Information
                </label>
                <input
                  type="text"
                  name="vehicle_info"
                  value={newBooking.vehicle_info}
                  onChange={handleInputChange}
                  placeholder="e.g., Toyota Avanza 2020"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Type
                </label>
                <select
                  name="service_type"
                  value={newBooking.service_type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Select Service</option>
                  {serviceTypes.map(service => (
                    <option key={service} value={service}>{service}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Booking Date
                </label>
                <input
                  type="date"
                  name="booking_date"
                  value={newBooking.booking_date}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={newBooking.phone}
                  onChange={handleInputChange}
                  placeholder="08xxxxxxxxxx"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes (Optional)
              </label>
              <textarea
                name="notes"
                value={newBooking.notes}
                onChange={handleInputChange}
                rows={3}
                placeholder="Additional notes or special requests..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            {newBooking.service_type && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">
                    Estimated Cost: Rp {getEstimatedCost(newBooking.service_type).toLocaleString()}
                  </span>
                </div>
              </div>
            )}
            <div className="flex space-x-4">
              <button
                type="submit"
                className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all"
              >
                Create Booking
              </button>
              <button
                type="button"
                onClick={() => setShowNewBooking(false)}
                className="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition-all"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Bookings List */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-900">Your Bookings</h2>
        </div>

        {myBookings.length === 0 ? (
          <div className="p-8 text-center">
            <Car className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No bookings yet</p>
            <p className="text-sm text-gray-400 mt-2">Create your first booking to get started</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {myBookings.map((booking) => (
              <div key={booking.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{booking.vehicle_info}</h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                        {getStatusIcon(booking.status)}
                        <span className="ml-1">{booking.status}</span>
                      </span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Car className="h-4 w-4" />
                        <span>{booking.service_type}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(booking.booking_date).toLocaleDateString('id-ID')}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-4 w-4" />
                        <span>Rp {booking.estimated_cost.toLocaleString()}</span>
                      </div>
                    </div>
                    {booking.notes && (
                      <div className="mt-2 text-sm text-gray-500">
                        <FileText className="h-4 w-4 inline mr-1" />
                        {booking.notes}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerBooking;
