// Simple API test
const http = require('http');

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3003,
      path,
      method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({ statusCode: res.statusCode, data: parsedData });
        } catch (e) {
          reject(new Error(`Invalid JSON response: ${responseData}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAPI() {
  console.log('🧪 Testing Mitra Garage API...\n');

  try {
    // Test health endpoint
    console.log('1. Testing Health Check...');
    const health = await makeRequest('GET', '/health');
    console.log(`✅ Health: ${health.data.status}`);

    // Test users endpoint
    console.log('\n2. Testing Users API...');
    const users = await makeRequest('GET', '/api/auth/users');
    console.log(`✅ Found ${users.data.length} users in database`);
    users.data.forEach(user => {
      console.log(`   - ${user.username} (${user.full_name}) - Role: ${user.role}`);
    });

    // Test login with admin
    console.log('\n3. Testing Admin Login...');
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };
    const login = await makeRequest('POST', '/api/auth/login', loginData);
    
    if (login.statusCode === 200) {
      console.log('✅ Admin login successful!');
      console.log(`   User: ${login.data.user.full_name}`);
      console.log(`   Role: ${login.data.user.role}`);
      console.log(`   Token: ${login.data.token.substring(0, 20)}...`);
    } else {
      console.log(`❌ Admin login failed: ${login.data.error}`);
    }

    // Test inventory endpoint
    console.log('\n4. Testing Inventory API...');
    const inventory = await makeRequest('GET', '/api/inventory');
    console.log(`✅ Found ${inventory.data.length} inventory items`);
    
    // Test bookings endpoint
    console.log('\n5. Testing Bookings API...');
    const bookings = await makeRequest('GET', '/api/bookings');
    console.log(`✅ Found ${bookings.data.length} bookings`);

    console.log('\n🎉 All API tests passed successfully!');
    console.log('\n📋 Available Login Credentials:');
    console.log('   👑 Admin: admin / admin123');
    console.log('   👨‍💼 Manager: manager / manager123');
    console.log('   🔧 Mechanic: joko / joko123');
    console.log('   👤 Staff: staff / staff123');

  } catch (error) {
    console.error('❌ API Test Failed:', error.message);
  }
}

testAPI();
