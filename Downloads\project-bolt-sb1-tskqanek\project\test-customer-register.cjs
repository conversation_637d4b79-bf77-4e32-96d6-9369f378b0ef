// Test customer registration
const http = require('http');

function testRegister(userData) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(userData);
    
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: '/api/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode === 201) {
            resolve(result);
          } else {
            reject(new Error(result.error || 'Registration failed'));
          }
        } catch (e) {
          reject(new Error('Invalid response'));
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

function testLogin(username, password) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({ username, password });
    
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode === 200) {
            resolve(result);
          } else {
            reject(new Error(result.error || 'Login failed'));
          }
        } catch (e) {
          reject(new Error('Invalid response'));
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

async function testCustomerRegistration() {
  console.log('👥 Testing Customer Registration...\n');

  const testCustomers = [
    {
      username: 'customer1',
      email: '<EMAIL>',
      password: 'customer123',
      full_name: 'John Doe'
    },
    {
      username: 'customer2',
      email: '<EMAIL>',
      password: 'customer456',
      full_name: 'Jane Smith'
    },
    {
      username: 'customer3',
      email: '<EMAIL>',
      password: 'customer789',
      full_name: 'Bob Johnson'
    }
  ];

  for (const customer of testCustomers) {
    try {
      console.log(`Registering ${customer.full_name}...`);
      const result = await testRegister(customer);
      console.log(`✅ Registration successful:`);
      console.log(`   User: ${result.user.full_name}`);
      console.log(`   Username: ${result.user.username}`);
      console.log(`   Email: ${result.user.email}`);
      console.log(`   Role: ${result.user.role}`);
      console.log(`   ID: ${result.user.id}`);
      
      // Test login immediately after registration
      console.log(`   Testing login...`);
      const loginResult = await testLogin(customer.username, customer.password);
      console.log(`   ✅ Login successful! Token: ${loginResult.token.substring(0, 20)}...`);
      console.log('');
    } catch (error) {
      console.log(`❌ Registration failed for ${customer.full_name}: ${error.message}\n`);
    }
  }

  console.log('🎉 Customer registration test completed!');
  console.log('\n📋 New Customer Accounts Created:');
  console.log('   👤 customer1 / customer123 (John Doe)');
  console.log('   👤 customer2 / customer456 (Jane Smith)');
  console.log('   👤 customer3 / customer789 (Bob Johnson)');
  console.log('\n💡 All customers have role: "customer"');
}

testCustomerRegistration().catch(console.error);
