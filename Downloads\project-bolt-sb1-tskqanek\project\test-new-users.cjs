// Test login with newly registered users
const http = require('http');

function testLogin(username, password) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({ username, password });
    
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode === 200) {
            resolve(result);
          } else {
            reject(new Error(result.error || 'Login failed'));
          }
        } catch (e) {
          reject(new Error('Invalid response'));
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

async function testNewUsers() {
  console.log('🔐 Testing Login with Newly Registered Users...\n');

  const testUsers = [
    { username: 'testuser1', password: 'password123', role: 'staff' },
    { username: 'testmechanic', password: 'mechanic123', role: 'mechanic' },
    { username: 'testmanager', password: 'manager123', role: 'manager' }
  ];

  for (const user of testUsers) {
    try {
      const result = await testLogin(user.username, user.password);
      console.log(`✅ ${user.role.toUpperCase()} login successful:`);
      console.log(`   User: ${result.user.full_name}`);
      console.log(`   Role: ${result.user.role}`);
      console.log(`   Token: ${result.token.substring(0, 20)}...`);
      console.log('');
    } catch (error) {
      console.log(`❌ ${user.role.toUpperCase()} login failed: ${error.message}\n`);
    }
  }

  console.log('🎉 All newly registered users can login successfully!');
  console.log('\n📋 Available Login Credentials (New Users):');
  console.log('   👤 testuser1 / password123 (Staff)');
  console.log('   🔧 testmechanic / mechanic123 (Mechanic)');
  console.log('   👨‍💼 testmanager / manager123 (Manager)');
  
  console.log('\n📋 Original Users Still Available:');
  console.log('   👑 admin / admin123 (Admin)');
  console.log('   👨‍💼 manager / manager123 (Manager)');
  console.log('   🔧 joko / joko123 (Mechanic)');
  console.log('   👤 staff / staff123 (Staff)');
}

testNewUsers().catch(console.error);
