// Test customer features - booking and order tracking
const fs = require('fs');
const path = require('path');

function simulateCustomerData() {
  console.log('🧪 Simulating Customer Data for Testing...\n');

  const customerEmail = '<EMAIL>';
  
  // Sample bookings data
  const sampleBookings = [
    {
      id: Date.now() - 86400000, // Yesterday
      customer_name: '<PERSON>',
      vehicle_info: 'Toyota Avanza 2020',
      service_type: 'Ganti Oli',
      booking_date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
      status: 'Selesai',
      estimated_cost: 150000,
      notes: 'Service rutin ganti oli mesin',
      phone: '081234567890',
      email: customerEmail
    },
    {
      id: Date.now() - 3600000, // 1 hour ago
      customer_name: '<PERSON>',
      vehicle_info: 'Toyota Avanza 2020',
      service_type: 'Tune Up',
      booking_date: new Date().toISOString().split('T')[0],
      status: '<PERSON><PERSON><PERSON>',
      estimated_cost: 300000,
      notes: 'Tune up lengkap dengan penggantian filter',
      phone: '081234567890',
      email: customerEmail
    },
    {
      id: Date.now(),
      customer_name: '<PERSON>',
      vehicle_info: 'Toyota Avanza 2020',
      service_type: 'Service AC',
      booking_date: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Tomorrow
      status: 'Dijadwalkan',
      estimated_cost: 200000,
      notes: 'Service AC dan pembersihan evaporator',
      phone: '081234567890',
      email: customerEmail
    }
  ];

  // Sample orders data
  const sampleOrders = [
    {
      id: Date.now() - 172800000, // 2 days ago
      order_date: new Date(Date.now() - 172800000).toISOString(),
      status: 'Delivered',
      total_amount: 225000,
      items: [
        {
          id: 1,
          name: 'Oli Mesin Mobil',
          price: 75000,
          quantity: 2,
          category: 'Oli Mesin'
        },
        {
          id: 2,
          name: 'Filter Udara',
          price: 45000,
          quantity: 1,
          category: 'Filter'
        },
        {
          id: 3,
          name: 'Busi NGK',
          price: 30000,
          quantity: 1,
          category: 'Spare Part'
        }
      ],
      customer_name: 'John Doe',
      customer_email: customerEmail,
      notes: 'Order placed from Browse Items'
    },
    {
      id: Date.now() - 86400000, // Yesterday
      order_date: new Date(Date.now() - 86400000).toISOString(),
      status: 'Shipped',
      total_amount: 650000,
      items: [
        {
          id: 4,
          name: 'Ban Mobil R15',
          price: 650000,
          quantity: 1,
          category: 'Ban'
        }
      ],
      customer_name: 'John Doe',
      customer_email: customerEmail,
      notes: 'Order placed from Browse Items'
    },
    {
      id: Date.now() - 3600000, // 1 hour ago
      order_date: new Date(Date.now() - 3600000).toISOString(),
      status: 'Processing',
      total_amount: 120000,
      items: [
        {
          id: 5,
          name: 'Wiper Blade',
          price: 60000,
          quantity: 2,
          category: 'Aksesoris'
        }
      ],
      customer_name: 'John Doe',
      customer_email: customerEmail,
      notes: 'Order placed from Browse Items'
    }
  ];

  // Create localStorage simulation files
  const localStorageData = {
    [`bookings_${customerEmail}`]: JSON.stringify(sampleBookings),
    [`orders_${customerEmail}`]: JSON.stringify(sampleOrders)
  };

  // Write to a JSON file for reference
  const dataFile = path.join(__dirname, 'customer-test-data.json');
  fs.writeFileSync(dataFile, JSON.stringify({
    customerEmail,
    bookings: sampleBookings,
    orders: sampleOrders,
    localStorageKeys: Object.keys(localStorageData)
  }, null, 2));

  console.log('✅ Sample data created for customer testing:');
  console.log(`📧 Customer Email: ${customerEmail}`);
  console.log(`📅 Bookings: ${sampleBookings.length} items`);
  console.log(`🛒 Orders: ${sampleOrders.length} items`);
  console.log(`📁 Data saved to: ${dataFile}`);
  
  console.log('\n📋 Sample Bookings:');
  sampleBookings.forEach((booking, index) => {
    console.log(`   ${index + 1}. ${booking.service_type} - ${booking.status} (Rp ${booking.estimated_cost.toLocaleString()})`);
  });

  console.log('\n🛒 Sample Orders:');
  sampleOrders.forEach((order, index) => {
    console.log(`   ${index + 1}. ${order.items.length} items - ${order.status} (Rp ${order.total_amount.toLocaleString()})`);
  });

  console.log('\n🔧 To test:');
  console.log('1. Login with: customer1 / customer123');
  console.log('2. Go to "My Bookings" to see service bookings');
  console.log('3. Go to "My Orders" to see item orders');
  console.log('4. Create new bookings and orders to test functionality');
  
  console.log('\n💡 LocalStorage Keys to check in browser:');
  Object.keys(localStorageData).forEach(key => {
    console.log(`   - ${key}`);
  });

  return {
    customerEmail,
    bookings: sampleBookings,
    orders: sampleOrders,
    localStorageData
  };
}

function generateLocalStorageScript(data) {
  console.log('\n📝 Generating localStorage setup script...');
  
  const script = `
// Run this in browser console to set up test data
// Copy and paste this entire block into browser console

${Object.entries(data.localStorageData).map(([key, value]) => 
  `localStorage.setItem('${key}', '${value.replace(/'/g, "\\'")}');`
).join('\n')}

console.log('✅ Test data loaded into localStorage');
console.log('🔄 Refresh the page to see the data');
`;

  const scriptFile = path.join(__dirname, 'setup-localStorage.js');
  fs.writeFileSync(scriptFile, script);
  
  console.log(`📁 Script saved to: ${scriptFile}`);
  console.log('📋 Copy the script content and paste it in browser console');
}

// Run the simulation
const testData = simulateCustomerData();
generateLocalStorageScript(testData);

console.log('\n🎉 Customer test data setup completed!');
console.log('\n🚀 Next steps:');
console.log('1. Open browser and login as customer1');
console.log('2. Open browser console (F12)');
console.log('3. Copy and paste the localStorage script');
console.log('4. Refresh the page');
console.log('5. Test My Bookings and My Orders features');
