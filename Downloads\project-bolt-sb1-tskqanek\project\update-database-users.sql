
-- Update Database Users to Match Application
-- Run this script in MySQL to sync users

-- 1. Update schema to support 'owner' role
ALTER TABLE users 
MODIFY COLUMN role ENUM('owner', 'admin', 'manager', 'mechanic', 'staff', 'customer') DEFAULT 'customer';

-- 2. Clear existing users
DELETE FROM users;
ALTER TABLE users AUTO_INCREMENT = 1;

-- 3. Insert users (passwords are base64 encoded for simplicity)
INSERT INTO users (username, email, password, full_name, role) VALUES
-- Owner
('owner', '<EMAIL>', 'b3duZXIxMjM=', 'Bengkel Owner', 'owner'),

-- Admin level
('admin', '<EMAIL>', 'YWRtaW4xMjM=', 'Administrator', 'admin'),
('manager', '<EMAIL>', 'bWFuYWdlcjEyMw==', 'Manager Bengkel', 'admin'),
('supervisor', '<EMAIL>', 'c3VwZXJ2aXNvcjEyMw==', 'Supervisor Bengkel', 'admin'),

-- Mechanics
('mechanic1', '<EMAIL>', 'bWVjaGFuaWMxMjM=', 'Joko Susilo', 'mechanic'),
('mechanic2', '<EMAIL>', 'bWVjaGFuaWMxMjM=', 'Ahmad Fauzi', 'mechanic'),
('joko', '<EMAIL>', 'am9rbzEyMw==', 'Joko Susilo', 'mechanic'),
('ahmad', '<EMAIL>', 'YWhtYWQxMjM=', 'Ahmad Fauzi', 'mechanic'),

-- Staff
('staff1', '<EMAIL>', 'c3RhZmYxMjM=', 'Bambang Sutopo', 'staff'),
('staff2', '<EMAIL>', 'c3RhZmYxMjM=', 'Siti Nurhaliza', 'staff'),
('staff', '<EMAIL>', 'c3RhZmYxMjM=', 'Staff Bengkel', 'staff'),

-- Customers
('customer1', '<EMAIL>', 'Y3VzdG9tZXIxMjM=', 'John Doe', 'customer'),
('customer2', '<EMAIL>', 'Y3VzdG9tZXIxMjM=', 'Jane Smith', 'customer'),
('customer3', '<EMAIL>', 'Y3VzdG9tZXIxMjM=', 'Robert Johnson', 'customer'),
('customer4', '<EMAIL>', 'Y3VzdG9tZXIxMjM=', 'Maria Garcia', 'customer');

-- 4. Verify users
SELECT id, username, email, full_name, role FROM users ORDER BY role, username;

-- 5. Check user count by role
SELECT role, COUNT(*) as count FROM users GROUP BY role ORDER BY role;
