{"name": "mitra-garage-backend", "version": "1.0.0", "description": "Backend API for Mitra Garage", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["garage", "workshop", "management", "api"], "author": "<PERSON><PERSON>arage", "license": "MIT", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "helmet": "^7.2.0", "morgan": "^1.10.0", "mysql2": "^3.14.2", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.0.2"}}