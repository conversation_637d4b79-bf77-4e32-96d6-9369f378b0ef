// Simple register test
const http = require('http');

function testRegister(userData) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(userData);
    
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: '/api/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode === 201) {
            resolve(result);
          } else {
            reject(new Error(result.error || 'Registration failed'));
          }
        } catch (e) {
          reject(new Error('Invalid response'));
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

async function runRegisterTests() {
  console.log('🔐 Testing Register System...\n');

  const testUsers = [
    {
      username: 'testuser1',
      email: '<EMAIL>',
      password: 'password123',
      full_name: 'Test User One',
      role: 'staff'
    },
    {
      username: 'testmechanic',
      email: '<EMAIL>',
      password: 'mechanic123',
      full_name: 'Test Mechanic',
      role: 'mechanic'
    },
    {
      username: 'testmanager',
      email: '<EMAIL>',
      password: 'manager123',
      full_name: 'Test Manager',
      role: 'manager'
    }
  ];

  for (const user of testUsers) {
    try {
      const result = await testRegister(user);
      console.log(`✅ ${user.role.toUpperCase()} registration successful:`);
      console.log(`   User: ${result.user.full_name}`);
      console.log(`   Username: ${result.user.username}`);
      console.log(`   Email: ${result.user.email}`);
      console.log(`   Role: ${result.user.role}`);
      console.log(`   ID: ${result.user.id}`);
      console.log('');
    } catch (error) {
      console.log(`❌ ${user.role.toUpperCase()} registration failed: ${error.message}\n`);
    }
  }

  // Test duplicate registration
  console.log('Testing duplicate registration...');
  try {
    await testRegister(testUsers[0]); // Try to register same user again
    console.log('❌ Duplicate registration should have failed');
  } catch (error) {
    console.log(`✅ Duplicate registration properly rejected: ${error.message}`);
  }

  // Test invalid email
  console.log('\nTesting invalid email...');
  try {
    await testRegister({
      username: 'invaliduser',
      email: 'invalid-email',
      password: 'password123',
      full_name: 'Invalid User',
      role: 'staff'
    });
    console.log('❌ Invalid email should have been rejected');
  } catch (error) {
    console.log(`✅ Invalid email properly rejected: ${error.message}`);
  }

  // Test short password
  console.log('\nTesting short password...');
  try {
    await testRegister({
      username: 'shortpass',
      email: '<EMAIL>',
      password: '123',
      full_name: 'Short Password User',
      role: 'staff'
    });
    console.log('❌ Short password should have been rejected');
  } catch (error) {
    console.log(`✅ Short password properly rejected: ${error.message}`);
  }

  console.log('\n🎉 Register system test completed!');
  console.log('\n📋 Test Users Created:');
  console.log('   👤 testuser1 / password123 (Staff)');
  console.log('   🔧 testmechanic / mechanic123 (Mechanic)');
  console.log('   👨‍💼 testmanager / manager123 (Manager)');
}

runRegisterTests().catch(console.error);
