// Simple login test without dependencies
const https = require('http');

function testLogin(username, password) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({ username, password });
    
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode === 200) {
            resolve(result);
          } else {
            reject(new Error(result.error || 'Login failed'));
          }
        } catch (e) {
          reject(new Error('Invalid response'));
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('🔐 Testing Login System...\n');

  const testUsers = [
    { username: 'admin', password: 'admin123', role: 'admin' },
    { username: 'manager', password: 'manager123', role: 'manager' },
    { username: 'joko', password: 'joko123', role: 'mechanic' },
    { username: 'staff', password: 'staff123', role: 'staff' }
  ];

  for (const user of testUsers) {
    try {
      const result = await testLogin(user.username, user.password);
      console.log(`✅ ${user.role.toUpperCase()} login successful:`);
      console.log(`   User: ${result.user.full_name}`);
      console.log(`   Role: ${result.user.role}`);
      console.log(`   Token: ${result.token.substring(0, 20)}...`);
      console.log('');
    } catch (error) {
      console.log(`❌ ${user.role.toUpperCase()} login failed: ${error.message}\n`);
    }
  }

  console.log('🎉 Login tests completed!');
  console.log('\n📋 Available Login Credentials:');
  console.log('   👑 Admin: admin / admin123');
  console.log('   👨‍💼 Manager: manager / manager123');
  console.log('   🔧 Mechanic: joko / joko123');
  console.log('   👤 Staff: staff / staff123');
}

runTests().catch(console.error);
