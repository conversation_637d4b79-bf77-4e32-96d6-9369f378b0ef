# 🗄️ DATABASE SETUP INSTRUCTIONS

## 📋 Cara Mengisi Database Users

Karena tabel `users` di database masih kosong, i<PERSON>ti langkah-langkah berikut untuk mengisinya:

### 🔧 OPTION 1: Menggunakan MySQL Interface (RECOMMENDED)

1. **Buka MySQL interface yang sudah terhubung**
2. **Pastikan database `mitra_garage` sudah dipilih**
3. **Copy dan paste script SQL berikut:**

```sql
-- Update schema untuk mendukung role 'owner'
ALTER TABLE users 
MODIFY COLUMN role ENUM('owner', 'admin', 'manager', 'mechanic', 'staff', 'customer') DEFAULT 'customer';

-- Clear existing users
DELETE FROM users;
ALTER TABLE users AUTO_INCREMENT = 1;

-- Insert 16 users dengan berbagai role
INSERT INTO users (username, email, password, full_name, role) VALUES
('owner', '<EMAIL>', 'b3duZXIxMjM=', 'Bengkel Owner', 'owner'),
('admin', '<EMAIL>', 'YWRtaW4xMjM=', 'Administrator', 'admin'),
('manager', '<EMAIL>', 'bWFuYWdlcjEyMw==', 'Manager Bengkel', 'admin'),
('supervisor', '<EMAIL>', 'c3VwZXJ2aXNvcjEyMw==', 'Supervisor Bengkel', 'admin'),
('mechanic1', '<EMAIL>', 'bWVjaGFuaWMxMjM=', 'Joko Susilo', 'mechanic'),
('mechanic2', '<EMAIL>', 'bWVjaGFuaWMxMjM=', 'Ahmad Fauzi', 'mechanic'),
('joko', '<EMAIL>', 'am9rbzEyMw==', 'Joko Susilo', 'mechanic'),
('ahmad', '<EMAIL>', 'YWhtYWQxMjM=', 'Ahmad Fauzi', 'mechanic'),
('staff1', '<EMAIL>', 'c3RhZmYxMjM=', 'Bambang Sutopo', 'staff'),
('staff2', '<EMAIL>', 'c3RhZmYxMjM=', 'Siti Nurhaliza', 'staff'),
('staff', '<EMAIL>', 'c3RhZmYxMjM=', 'Staff Bengkel', 'staff'),
('customer1', '<EMAIL>', 'Y3VzdG9tZXIxMjM=', 'John Doe', 'customer'),
('customer2', '<EMAIL>', 'Y3VzdG9tZXIxMjM=', 'Jane Smith', 'customer'),
('customer3', '<EMAIL>', 'Y3VzdG9tZXIxMjM=', 'Robert Johnson', 'customer'),
('customer4', '<EMAIL>', 'Y3VzdG9tZXIxMjM=', 'Maria Garcia', 'customer');

-- Verify data
SELECT id, username, email, full_name, role FROM users ORDER BY role, username;
```

4. **Execute script tersebut**
5. **Refresh tabel users untuk melihat hasilnya**

### 🚀 OPTION 2: Menggunakan File SQL

1. **Gunakan file `populate-users.sql` yang sudah dibuat**
2. **Di MySQL interface, pilih File > Run SQL Script**
3. **Pilih file `populate-users.sql`**
4. **Execute script**

### 🧪 OPTION 3: Menggunakan Command Line (jika MySQL CLI tersedia)

```bash
mysql -u root -p mitra_garage < populate-users.sql
```

## 🔑 LOGIN CREDENTIALS SETELAH DATABASE TERISI

### 👑 OWNER (Full Access - 8 Menus)
- **Username:** `owner`
- **Password:** `owner123`
- **Access:** Dashboard, Inventaris, Booking, Riwayat, Testimoni, Admin Panel, User Management, Reports

### 🔧 ADMIN (Management Access - 6 Menus)
- **Username:** `admin`
- **Password:** `admin123`
- **Access:** Dashboard, Inventaris, Booking, Riwayat, Testimoni, Admin Panel

### 📊 MANAGER (Admin Level - 6 Menus)
- **Username:** `manager`
- **Password:** `manager123`
- **Access:** Same as admin

### 👨‍💼 SUPERVISOR (Admin Level - 6 Menus)
- **Username:** `supervisor`
- **Password:** `supervisor123`
- **Access:** Same as admin

### 👨‍🔧 MECHANIC (Operational Access - 5 Menus)
- **Username:** `mechanic1`
- **Password:** `mechanic123`
- **Access:** Dashboard, Inventaris, Booking, Riwayat, Testimoni

### 👨‍💼 STAFF (Basic Access - 5 Menus)
- **Username:** `staff1`
- **Password:** `staff123`
- **Access:** Dashboard, Inventaris, Booking, Riwayat, Testimoni

### 👤 CUSTOMER (Customer Access - 4 Menus)
- **Username:** `customer1`
- **Password:** `customer123`
- **Access:** Dashboard, Browse Items, My Bookings, My Orders

## 📊 EXPECTED RESULT

Setelah menjalankan script, tabel `users` akan berisi:

| Role | Count | Description |
|------|-------|-------------|
| owner | 1 | Full system access |
| admin | 3 | Management access |
| mechanic | 4 | Operational access |
| staff | 3 | Basic access |
| customer | 5 | Customer access |
| **TOTAL** | **16** | **All role levels** |

## ✅ VERIFICATION STEPS

1. **Check users table:**
   ```sql
   SELECT COUNT(*) FROM users;
   -- Should return 16
   ```

2. **Check role distribution:**
   ```sql
   SELECT role, COUNT(*) as count FROM users GROUP BY role;
   ```

3. **Test login in application:**
   - Try logging in with `owner / owner123`
   - Should see 8 menu items including User Management
   - Try logging in with `admin / admin123`
   - Should see 6 menu items without User Management

## 🔒 PASSWORD ENCODING

Passwords are base64 encoded (same as application):
- `owner123` → `b3duZXIxMjM=`
- `admin123` → `YWRtaW4xMjM=`
- `customer123` → `Y3VzdG9tZXIxMjM=`

## 🎯 NEXT STEPS

1. ✅ Execute SQL script above
2. ✅ Verify 16 users are inserted
3. ✅ Test login with different roles
4. ✅ Verify role-based access control
5. ✅ Check User Management (owner only)
6. ✅ Check Reports & Analytics (owner only)

## 🚨 TROUBLESHOOTING

### If you get "Data truncated for column 'role'" error:
```sql
-- Run this first to fix schema
ALTER TABLE users 
MODIFY COLUMN role ENUM('owner', 'admin', 'manager', 'mechanic', 'staff', 'customer') DEFAULT 'customer';
```

### If users table doesn't exist:
```sql
-- Create users table
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  full_name VARCHAR(100) NOT NULL,
  role ENUM('owner', 'admin', 'manager', 'mechanic', 'staff', 'customer') DEFAULT 'customer',
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🎉 SUCCESS!

Once completed, your database will be fully synchronized with the application, and you can test all role-based features!
